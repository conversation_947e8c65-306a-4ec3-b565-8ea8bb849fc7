{"ast": null, "code": "import axios from 'axios';\n\n// Unsplash API\nexport const unsplashApi = {\n  // 搜索图片\n  searchPhotos(query, page = 1, perPage = 10) {\n    return axios.get('/api/unsplash/search', {\n      params: {\n        query,\n        page,\n        per_page: perPage\n      }\n    });\n  },\n  // 获取随机图片\n  getRandomPhoto(query) {\n    return axios.get('/api/unsplash/random', {\n      params: {\n        query\n      }\n    });\n  }\n};\n\n// 设备API\nexport const equipmentApi = {\n  // 获取设备列表\n  getEquipments(params) {\n    return axios.get('/api/equipment', {\n      params\n    });\n  },\n  // 获取设备详情\n  getEquipment(id) {\n    return axios.get(`/api/equipment/${id}`);\n  },\n  // 获取设备类别\n  getCategories() {\n    return axios.get('/api/equipment/categories');\n  },\n  // 获取设备可用性\n  getAvailability(id, startDate, endDate) {\n    return axios.get(`/api/equipment/${id}/availability`, {\n      params: {\n        start_date: startDate,\n        end_date: endDate\n      }\n    });\n  },\n  // 创建设备（管理员）\n  createEquipment(data) {\n    return axios.post('/api/equipment', data);\n  },\n  // 更新设备（管理员）\n  updateEquipment(id, data) {\n    return axios.put(`/api/equipment/${id}`, data);\n  },\n  // 删除设备（管理员）\n  deleteEquipment(id) {\n    return axios.delete(`/api/equipment/${id}`);\n  }\n};\n\n// 设备类别API\nexport const categoryApi = {\n  // 获取设备类别列表\n  getCategories(params) {\n    return axios.get('/api/equipment-categories', {\n      params\n    });\n  },\n  // 获取所有设备类别\n  getAllCategories() {\n    return axios.get('/api/equipment-categories/all');\n  },\n  // 获取设备类别详情\n  getCategory(id) {\n    return axios.get(`/api/equipment-categories/${id}`);\n  },\n  // 创建设备类别（管理员）\n  createCategory(data) {\n    return axios.post('/api/equipment-categories', data);\n  },\n  // 更新设备类别（管理员）\n  updateCategory(id, data) {\n    return axios.put(`/api/equipment-categories/${id}`, data);\n  },\n  // 删除设备类别（管理员）\n  deleteCategory(id) {\n    return axios.delete(`/api/equipment-categories/${id}`);\n  }\n};\n\n// 预定API\nexport const reservationApi = {\n  // 创建预定\n  createReservation(data) {\n    return axios.post('/api/reservation', data);\n  },\n  // 获取预定列表（管理员）\n  getReservations(params) {\n    return axios.get('/api/reservation', {\n      params\n    });\n  },\n  // 获取预定详情\n  getReservation(code) {\n    return axios.get(`/api/reservation/${code}`);\n  },\n  // 通过预约序号获取预约详情\n  getReservationByNumber(reservationNumber) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime();\n    const url = `/api/reservation/number/${reservationNumber}?_t=${timestamp}`;\n    console.log(`通过预约序号获取预约详情URL: ${url}`);\n    return axios.get(url);\n  },\n  // 通过预定码或预约序号获取预定详情\n  getReservationByCode(code, reservationNumber = null) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime();\n\n    // 构建基本URL\n    let url = code.includes('?') ? `${code}&_t=${timestamp}` : `/api/reservation/code/${code}?_t=${timestamp}`;\n\n    // 如果提供了预约序号，添加到URL参数中\n    if (reservationNumber) {\n      // 处理预约序号参数，确保它是字符串格式\n      let reservationNumberStr = '';\n      if (typeof reservationNumber === 'object') {\n        console.log('预约序号参数是对象类型，尝试提取预约序号:', reservationNumber);\n        if (reservationNumber.reservation_number) {\n          reservationNumberStr = String(reservationNumber.reservation_number);\n          console.log(`从对象中提取预约序号: ${reservationNumberStr}`);\n        } else {\n          console.warn('预约序号参数是对象但没有reservation_number字段，忽略此参数');\n          // 不再序列化整个对象，直接忽略\n          reservationNumberStr = '';\n        }\n      } else if (typeof reservationNumber === 'string' && reservationNumber.trim()) {\n        // 确保是有效的预约序号格式（通常以RN-开头）\n        if (reservationNumber.startsWith('RN-') || reservationNumber.match(/^[A-Z0-9-]+$/)) {\n          reservationNumberStr = reservationNumber.trim();\n          console.log(`预约序号参数是字符串类型: ${reservationNumberStr}`);\n        } else {\n          console.warn(`预约序号格式不正确，忽略: ${reservationNumber}`);\n          reservationNumberStr = '';\n        }\n      } else {\n        console.warn(`预约序号参数类型不支持: ${typeof reservationNumber}, 值: ${reservationNumber}`);\n        reservationNumberStr = '';\n      }\n      if (reservationNumberStr) {\n        url += `&reservation_number=${encodeURIComponent(reservationNumberStr)}`;\n        console.log(`添加预约序号参数: ${reservationNumberStr}`);\n      }\n    }\n    console.log(`获取预约详情URL: ${url}`);\n    return axios.get(url);\n  },\n  // 通过预约序号获取预定详情\n  getReservationByNumber(reservationNumber) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime();\n    const url = `/api/reservation/number/${reservationNumber}?_t=${timestamp}`;\n    console.log(`通过预约序号获取预约详情URL: ${url}`);\n    return axios.get(url);\n  },\n  // 更新预定\n  updateReservation(code, data, reservationNumber = null) {\n    if (reservationNumber) {\n      // 如果提供了预约序号，使用专门的预约序号API\n      return axios.put(`/api/reservation/number/${reservationNumber}`, data);\n    } else {\n      // 否则使用原有的预约码API\n      return axios.put(`/api/reservation/code/${code}`, data);\n    }\n  },\n  // 通过预约序号更新预定\n  updateReservationByNumber(reservationNumber, data) {\n    return axios.put(`/api/reservation/number/${reservationNumber}`, data);\n  },\n  // 通过预约码更新预定（支持预约序号参数）\n  updateReservationByCode(code, data, reservationNumber = null) {\n    const params = reservationNumber ? {\n      reservation_number: reservationNumber\n    } : {};\n    return axios.put(`/api/reservation/code/${code}`, data, {\n      params\n    });\n  },\n  // 通过预约码获取预定详情（支持查询参数）\n  getReservationByCodeWithParams(code, params = {}) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime();\n    const queryParams = {\n      ...params,\n      _t: timestamp\n    };\n    console.log(`通过预约码获取预定详情（带参数）: ${code}`, queryParams);\n    return axios.get(`/api/reservation/code/${code}`, {\n      params: queryParams\n    });\n  },\n  // 取消预定\n  cancelReservation(code, data) {\n    return axios.post(`/api/reservation/cancel/code/${code}`, data);\n  },\n  // 获取预定二维码\n  getReservationQrcode(code) {\n    return axios.get(`/api/reservation/qrcode/${code}`);\n  },\n  // 获取预定历史记录\n  getReservationHistory(code, reservationNumber = null) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime();\n    let url = `/api/reservation/code/${code}/history?_t=${timestamp}`;\n\n    // 如果提供了预约序号，添加到URL中\n    if (reservationNumber) {\n      url += `&reservation_number=${encodeURIComponent(reservationNumber)}`;\n    }\n    return axios.get(url);\n  },\n  // 导出预定数据\n  exportReservations(exportData) {\n    return axios.post('/api/reservation/export', exportData, {\n      responseType: 'blob' // 重要：设置响应类型为blob以处理文件下载\n    });\n  }\n};\n\n// 循环预约API\nexport const recurringReservationApi = {\n  // 创建循环预约\n  createRecurringReservation(data) {\n    return axios.post('/api/recurring-reservation', data);\n  },\n  // 获取循环预约列表\n  getRecurringReservations(params) {\n    return axios.get('/api/recurring-reservation', {\n      params\n    });\n  },\n  // 获取循环预约详情\n  getRecurringReservation(id) {\n    return axios.get(`/api/recurring-reservation/${id}`);\n  },\n  // 通过预定码获取循环预约详情\n  getRecurringReservationByCode(code) {\n    return axios.get(`/api/recurring-reservation/code/${code}`);\n  },\n  // 更新循环预约\n  updateRecurringReservation(id, data, updateFutureOnly = 1) {\n    return axios.put(`/api/recurring-reservation/${id}`, data, {\n      params: {\n        update_future_only: updateFutureOnly\n      }\n    });\n  },\n  // 取消循环预约\n  cancelRecurringReservation(id, userEmail = null, lang = 'zh_CN') {\n    return axios.post(`/api/recurring-reservation/cancel/${id}`, null, {\n      params: {\n        user_email: userEmail,\n        lang: lang\n      }\n    });\n  },\n  // 获取循环预约的子预约\n  getChildReservations(id, includePast = 0) {\n    return axios.get(`/api/recurring-reservation/${id}/reservations`, {\n      params: {\n        include_past: includePast\n      }\n    });\n  }\n};\n\n// 管理员API\nexport const adminApi = {\n  // 管理员登录\n  login(username, password) {\n    const formData = new FormData();\n    formData.append('username', username);\n    formData.append('password', password);\n    return axios.post('/api/admin/login', formData);\n  },\n  // 获取管理员列表（超级管理员）\n  getAdmins(params) {\n    return axios.get('/api/admin', {\n      params\n    });\n  },\n  // 创建管理员（超级管理员）\n  createAdmin(data) {\n    return axios.post('/api/admin', data);\n  },\n  // 更新管理员\n  updateAdmin(id, data) {\n    return axios.put(`/api/admin/${id}`, data);\n  },\n  // 删除管理员（超级管理员）\n  deleteAdmin(id) {\n    return axios.delete(`/api/admin/${id}`);\n  },\n  // 获取系统设置\n  getSettings() {\n    return axios.get('/api/admin/settings');\n  },\n  // 更新系统设置\n  updateSettings(data) {\n    return axios.put('/api/admin/settings', data);\n  }\n};\n\n// 用户API\nexport const userApi = {\n  // 用户登录\n  login(credentials) {\n    return axios.post('/api/user/login', credentials);\n  },\n  // 获取当前用户信息\n  getProfile() {\n    return axios.get('/api/user/profile');\n  }\n};\n\n// 公告API\nexport const announcementApi = {\n  // 获取所有公告\n  getAnnouncements(params) {\n    return axios.get('/api/announcements', {\n      params\n    });\n  },\n  // 获取单个公告\n  getAnnouncement(id) {\n    return axios.get(`/api/announcements/${id}`);\n  },\n  // 创建公告\n  createAnnouncement(data) {\n    return axios.post('/api/announcements', data);\n  },\n  // 更新公告\n  updateAnnouncement(id, data) {\n    return axios.put(`/api/announcements/${id}`, data);\n  },\n  // 删除公告\n  deleteAnnouncement(id) {\n    return axios.delete(`/api/announcements/${id}`);\n  }\n};\n\n// 导出API对象供Vue组件使用\nexport const api = {\n  equipment: equipmentApi,\n  reservation: reservationApi,\n  recurringReservation: recurringReservationApi,\n  category: categoryApi,\n  user: userApi,\n  admin: adminApi,\n  announcement: announcementApi\n};", "map": {"version": 3, "names": ["axios", "unsplashApi", "searchPhotos", "query", "page", "perPage", "get", "params", "per_page", "getRandomPhoto", "equipmentApi", "getEquipments", "getEquipment", "id", "getCategories", "getAvailability", "startDate", "endDate", "start_date", "end_date", "createEquipment", "data", "post", "updateEquipment", "put", "deleteEquipment", "delete", "categoryApi", "getAllCategories", "getCategory", "createCategory", "updateCategory", "deleteCategory", "reservationApi", "createReservation", "getReservations", "getReservation", "code", "getReservationByNumber", "reservationNumber", "timestamp", "Date", "getTime", "url", "console", "log", "getReservationByCode", "includes", "reservationNumberStr", "reservation_number", "String", "warn", "trim", "startsWith", "match", "encodeURIComponent", "updateReservation", "updateReservationByNumber", "updateReservationByCode", "getReservationByCodeWithParams", "queryParams", "_t", "cancelReservation", "getReservationQrcode", "getReservationHistory", "exportReservations", "exportData", "responseType", "recurringReservationApi", "createRecurringReservation", "getRecurringReservations", "getRecurringReservation", "getRecurringReservationByCode", "updateRecurringReservation", "updateFutureOnly", "update_future_only", "cancelRecurringReservation", "userEmail", "lang", "user_email", "getChildReservations", "includePast", "include_past", "adminApi", "login", "username", "password", "formData", "FormData", "append", "get<PERSON>dmins", "createAdmin", "updateAdmin", "deleteAdmin", "getSettings", "updateSettings", "userApi", "credentials", "getProfile", "announcementApi", "getAnnouncements", "getAnnouncement", "createAnnouncement", "updateAnnouncement", "deleteAnnouncement", "api", "equipment", "reservation", "recurringReservation", "category", "user", "admin", "announcement"], "sources": ["D:/Equipment-Reservation-System-main/frontend/src/api/index.js"], "sourcesContent": ["import axios from 'axios'\n\n// Unsplash API\nexport const unsplashApi = {\n  // 搜索图片\n  searchPhotos(query, page = 1, perPage = 10) {\n    return axios.get('/api/unsplash/search', {\n      params: {\n        query,\n        page,\n        per_page: perPage\n      }\n    })\n  },\n\n  // 获取随机图片\n  getRandomPhoto(query) {\n    return axios.get('/api/unsplash/random', {\n      params: { query }\n    })\n  }\n}\n\n// 设备API\nexport const equipmentApi = {\n  // 获取设备列表\n  getEquipments(params) {\n    return axios.get('/api/equipment', { params })\n  },\n\n  // 获取设备详情\n  getEquipment(id) {\n    return axios.get(`/api/equipment/${id}`)\n  },\n\n  // 获取设备类别\n  getCategories() {\n    return axios.get('/api/equipment/categories')\n  },\n\n  // 获取设备可用性\n  getAvailability(id, startDate, endDate) {\n    return axios.get(`/api/equipment/${id}/availability`, {\n      params: { start_date: startDate, end_date: endDate }\n    })\n  },\n\n  // 创建设备（管理员）\n  createEquipment(data) {\n    return axios.post('/api/equipment', data)\n  },\n\n  // 更新设备（管理员）\n  updateEquipment(id, data) {\n    return axios.put(`/api/equipment/${id}`, data)\n  },\n\n  // 删除设备（管理员）\n  deleteEquipment(id) {\n    return axios.delete(`/api/equipment/${id}`)\n  }\n}\n\n// 设备类别API\nexport const categoryApi = {\n  // 获取设备类别列表\n  getCategories(params) {\n    return axios.get('/api/equipment-categories', { params })\n  },\n\n  // 获取所有设备类别\n  getAllCategories() {\n    return axios.get('/api/equipment-categories/all')\n  },\n\n  // 获取设备类别详情\n  getCategory(id) {\n    return axios.get(`/api/equipment-categories/${id}`)\n  },\n\n  // 创建设备类别（管理员）\n  createCategory(data) {\n    return axios.post('/api/equipment-categories', data)\n  },\n\n  // 更新设备类别（管理员）\n  updateCategory(id, data) {\n    return axios.put(`/api/equipment-categories/${id}`, data)\n  },\n\n  // 删除设备类别（管理员）\n  deleteCategory(id) {\n    return axios.delete(`/api/equipment-categories/${id}`)\n  }\n}\n\n// 预定API\nexport const reservationApi = {\n  // 创建预定\n  createReservation(data) {\n    return axios.post('/api/reservation', data)\n  },\n\n  // 获取预定列表（管理员）\n  getReservations(params) {\n    return axios.get('/api/reservation', { params })\n  },\n\n  // 获取预定详情\n  getReservation(code) {\n    return axios.get(`/api/reservation/${code}`)\n  },\n\n  // 通过预约序号获取预约详情\n  getReservationByNumber(reservationNumber) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime()\n    const url = `/api/reservation/number/${reservationNumber}?_t=${timestamp}`\n\n    console.log(`通过预约序号获取预约详情URL: ${url}`)\n    return axios.get(url)\n  },\n\n  // 通过预定码或预约序号获取预定详情\n  getReservationByCode(code, reservationNumber = null) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime()\n\n    // 构建基本URL\n    let url = code.includes('?')\n      ? `${code}&_t=${timestamp}`\n      : `/api/reservation/code/${code}?_t=${timestamp}`\n\n    // 如果提供了预约序号，添加到URL参数中\n    if (reservationNumber) {\n      // 处理预约序号参数，确保它是字符串格式\n      let reservationNumberStr = '';\n\n      if (typeof reservationNumber === 'object') {\n        console.log('预约序号参数是对象类型，尝试提取预约序号:', reservationNumber);\n        if (reservationNumber.reservation_number) {\n          reservationNumberStr = String(reservationNumber.reservation_number);\n          console.log(`从对象中提取预约序号: ${reservationNumberStr}`);\n        } else {\n          console.warn('预约序号参数是对象但没有reservation_number字段，忽略此参数');\n          // 不再序列化整个对象，直接忽略\n          reservationNumberStr = '';\n        }\n      } else if (typeof reservationNumber === 'string' && reservationNumber.trim()) {\n        // 确保是有效的预约序号格式（通常以RN-开头）\n        if (reservationNumber.startsWith('RN-') || reservationNumber.match(/^[A-Z0-9-]+$/)) {\n          reservationNumberStr = reservationNumber.trim();\n          console.log(`预约序号参数是字符串类型: ${reservationNumberStr}`);\n        } else {\n          console.warn(`预约序号格式不正确，忽略: ${reservationNumber}`);\n          reservationNumberStr = '';\n        }\n      } else {\n        console.warn(`预约序号参数类型不支持: ${typeof reservationNumber}, 值: ${reservationNumber}`);\n        reservationNumberStr = '';\n      }\n\n      if (reservationNumberStr) {\n        url += `&reservation_number=${encodeURIComponent(reservationNumberStr)}`;\n        console.log(`添加预约序号参数: ${reservationNumberStr}`);\n      }\n    }\n\n    console.log(`获取预约详情URL: ${url}`)\n    return axios.get(url)\n  },\n\n  // 通过预约序号获取预定详情\n  getReservationByNumber(reservationNumber) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime()\n    const url = `/api/reservation/number/${reservationNumber}?_t=${timestamp}`\n\n    console.log(`通过预约序号获取预约详情URL: ${url}`)\n    return axios.get(url)\n  },\n\n  // 更新预定\n  updateReservation(code, data, reservationNumber = null) {\n    if (reservationNumber) {\n      // 如果提供了预约序号，使用专门的预约序号API\n      return axios.put(`/api/reservation/number/${reservationNumber}`, data)\n    } else {\n      // 否则使用原有的预约码API\n      return axios.put(`/api/reservation/code/${code}`, data)\n    }\n  },\n\n  // 通过预约序号更新预定\n  updateReservationByNumber(reservationNumber, data) {\n    return axios.put(`/api/reservation/number/${reservationNumber}`, data)\n  },\n\n  // 通过预约码更新预定（支持预约序号参数）\n  updateReservationByCode(code, data, reservationNumber = null) {\n    const params = reservationNumber ? { reservation_number: reservationNumber } : {}\n    return axios.put(`/api/reservation/code/${code}`, data, { params })\n  },\n\n  // 通过预约码获取预定详情（支持查询参数）\n  getReservationByCodeWithParams(code, params = {}) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime()\n    const queryParams = { ...params, _t: timestamp }\n\n    console.log(`通过预约码获取预定详情（带参数）: ${code}`, queryParams)\n    return axios.get(`/api/reservation/code/${code}`, { params: queryParams })\n  },\n\n  // 取消预定\n  cancelReservation(code, data) {\n    return axios.post(`/api/reservation/cancel/code/${code}`, data)\n  },\n\n  // 获取预定二维码\n  getReservationQrcode(code) {\n    return axios.get(`/api/reservation/qrcode/${code}`)\n  },\n\n  // 获取预定历史记录\n  getReservationHistory(code, reservationNumber = null) {\n    // 添加时间戳参数，防止缓存\n    const timestamp = new Date().getTime()\n    let url = `/api/reservation/code/${code}/history?_t=${timestamp}`\n\n    // 如果提供了预约序号，添加到URL中\n    if (reservationNumber) {\n      url += `&reservation_number=${encodeURIComponent(reservationNumber)}`\n    }\n\n    return axios.get(url)\n  },\n\n  // 导出预定数据\n  exportReservations(exportData) {\n    return axios.post('/api/reservation/export', exportData, {\n      responseType: 'blob' // 重要：设置响应类型为blob以处理文件下载\n    })\n  }\n}\n\n// 循环预约API\nexport const recurringReservationApi = {\n  // 创建循环预约\n  createRecurringReservation(data) {\n    return axios.post('/api/recurring-reservation', data)\n  },\n\n  // 获取循环预约列表\n  getRecurringReservations(params) {\n    return axios.get('/api/recurring-reservation', { params })\n  },\n\n  // 获取循环预约详情\n  getRecurringReservation(id) {\n    return axios.get(`/api/recurring-reservation/${id}`)\n  },\n\n  // 通过预定码获取循环预约详情\n  getRecurringReservationByCode(code) {\n    return axios.get(`/api/recurring-reservation/code/${code}`)\n  },\n\n  // 更新循环预约\n  updateRecurringReservation(id, data, updateFutureOnly = 1) {\n    return axios.put(`/api/recurring-reservation/${id}`, data, {\n      params: { update_future_only: updateFutureOnly }\n    })\n  },\n\n  // 取消循环预约\n  cancelRecurringReservation(id, userEmail = null, lang = 'zh_CN') {\n    return axios.post(`/api/recurring-reservation/cancel/${id}`, null, {\n      params: {\n        user_email: userEmail,\n        lang: lang\n      }\n    })\n  },\n\n  // 获取循环预约的子预约\n  getChildReservations(id, includePast = 0) {\n    return axios.get(`/api/recurring-reservation/${id}/reservations`, {\n      params: { include_past: includePast }\n    })\n  }\n}\n\n// 管理员API\nexport const adminApi = {\n  // 管理员登录\n  login(username, password) {\n    const formData = new FormData()\n    formData.append('username', username)\n    formData.append('password', password)\n    return axios.post('/api/admin/login', formData)\n  },\n\n  // 获取管理员列表（超级管理员）\n  getAdmins(params) {\n    return axios.get('/api/admin', { params })\n  },\n\n  // 创建管理员（超级管理员）\n  createAdmin(data) {\n    return axios.post('/api/admin', data)\n  },\n\n  // 更新管理员\n  updateAdmin(id, data) {\n    return axios.put(`/api/admin/${id}`, data)\n  },\n\n  // 删除管理员（超级管理员）\n  deleteAdmin(id) {\n    return axios.delete(`/api/admin/${id}`)\n  },\n\n  // 获取系统设置\n  getSettings() {\n    return axios.get('/api/admin/settings')\n  },\n\n  // 更新系统设置\n  updateSettings(data) {\n    return axios.put('/api/admin/settings', data)\n  }\n}\n\n// 用户API\nexport const userApi = {\n  // 用户登录\n  login(credentials) {\n    return axios.post('/api/user/login', credentials)\n  },\n\n  // 获取当前用户信息\n  getProfile() {\n    return axios.get('/api/user/profile')\n  }\n}\n\n// 公告API\nexport const announcementApi = {\n  // 获取所有公告\n  getAnnouncements(params) {\n    return axios.get('/api/announcements', { params })\n  },\n\n  // 获取单个公告\n  getAnnouncement(id) {\n    return axios.get(`/api/announcements/${id}`)\n  },\n\n  // 创建公告\n  createAnnouncement(data) {\n    return axios.post('/api/announcements', data)\n  },\n\n  // 更新公告\n  updateAnnouncement(id, data) {\n    return axios.put(`/api/announcements/${id}`, data)\n  },\n\n  // 删除公告\n  deleteAnnouncement(id) {\n    return axios.delete(`/api/announcements/${id}`)\n  }\n}\n\n// 导出API对象供Vue组件使用\nexport const api = {\n  equipment: equipmentApi,\n  reservation: reservationApi,\n  recurringReservation: recurringReservationApi,\n  category: categoryApi,\n  user: userApi,\n  admin: adminApi,\n  announcement: announcementApi\n}\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;;AAEzB;AACA,OAAO,MAAMC,WAAW,GAAG;EACzB;EACAC,YAAYA,CAACC,KAAK,EAAEC,IAAI,GAAG,CAAC,EAAEC,OAAO,GAAG,EAAE,EAAE;IAC1C,OAAOL,KAAK,CAACM,GAAG,CAAC,sBAAsB,EAAE;MACvCC,MAAM,EAAE;QACNJ,KAAK;QACLC,IAAI;QACJI,QAAQ,EAAEH;MACZ;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAI,cAAcA,CAACN,KAAK,EAAE;IACpB,OAAOH,KAAK,CAACM,GAAG,CAAC,sBAAsB,EAAE;MACvCC,MAAM,EAAE;QAAEJ;MAAM;IAClB,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMO,YAAY,GAAG;EAC1B;EACAC,aAAaA,CAACJ,MAAM,EAAE;IACpB,OAAOP,KAAK,CAACM,GAAG,CAAC,gBAAgB,EAAE;MAAEC;IAAO,CAAC,CAAC;EAChD,CAAC;EAED;EACAK,YAAYA,CAACC,EAAE,EAAE;IACf,OAAOb,KAAK,CAACM,GAAG,CAAC,kBAAkBO,EAAE,EAAE,CAAC;EAC1C,CAAC;EAED;EACAC,aAAaA,CAAA,EAAG;IACd,OAAOd,KAAK,CAACM,GAAG,CAAC,2BAA2B,CAAC;EAC/C,CAAC;EAED;EACAS,eAAeA,CAACF,EAAE,EAAEG,SAAS,EAAEC,OAAO,EAAE;IACtC,OAAOjB,KAAK,CAACM,GAAG,CAAC,kBAAkBO,EAAE,eAAe,EAAE;MACpDN,MAAM,EAAE;QAAEW,UAAU,EAAEF,SAAS;QAAEG,QAAQ,EAAEF;MAAQ;IACrD,CAAC,CAAC;EACJ,CAAC;EAED;EACAG,eAAeA,CAACC,IAAI,EAAE;IACpB,OAAOrB,KAAK,CAACsB,IAAI,CAAC,gBAAgB,EAAED,IAAI,CAAC;EAC3C,CAAC;EAED;EACAE,eAAeA,CAACV,EAAE,EAAEQ,IAAI,EAAE;IACxB,OAAOrB,KAAK,CAACwB,GAAG,CAAC,kBAAkBX,EAAE,EAAE,EAAEQ,IAAI,CAAC;EAChD,CAAC;EAED;EACAI,eAAeA,CAACZ,EAAE,EAAE;IAClB,OAAOb,KAAK,CAAC0B,MAAM,CAAC,kBAAkBb,EAAE,EAAE,CAAC;EAC7C;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,WAAW,GAAG;EACzB;EACAb,aAAaA,CAACP,MAAM,EAAE;IACpB,OAAOP,KAAK,CAACM,GAAG,CAAC,2BAA2B,EAAE;MAAEC;IAAO,CAAC,CAAC;EAC3D,CAAC;EAED;EACAqB,gBAAgBA,CAAA,EAAG;IACjB,OAAO5B,KAAK,CAACM,GAAG,CAAC,+BAA+B,CAAC;EACnD,CAAC;EAED;EACAuB,WAAWA,CAAChB,EAAE,EAAE;IACd,OAAOb,KAAK,CAACM,GAAG,CAAC,6BAA6BO,EAAE,EAAE,CAAC;EACrD,CAAC;EAED;EACAiB,cAAcA,CAACT,IAAI,EAAE;IACnB,OAAOrB,KAAK,CAACsB,IAAI,CAAC,2BAA2B,EAAED,IAAI,CAAC;EACtD,CAAC;EAED;EACAU,cAAcA,CAAClB,EAAE,EAAEQ,IAAI,EAAE;IACvB,OAAOrB,KAAK,CAACwB,GAAG,CAAC,6BAA6BX,EAAE,EAAE,EAAEQ,IAAI,CAAC;EAC3D,CAAC;EAED;EACAW,cAAcA,CAACnB,EAAE,EAAE;IACjB,OAAOb,KAAK,CAAC0B,MAAM,CAAC,6BAA6Bb,EAAE,EAAE,CAAC;EACxD;AACF,CAAC;;AAED;AACA,OAAO,MAAMoB,cAAc,GAAG;EAC5B;EACAC,iBAAiBA,CAACb,IAAI,EAAE;IACtB,OAAOrB,KAAK,CAACsB,IAAI,CAAC,kBAAkB,EAAED,IAAI,CAAC;EAC7C,CAAC;EAED;EACAc,eAAeA,CAAC5B,MAAM,EAAE;IACtB,OAAOP,KAAK,CAACM,GAAG,CAAC,kBAAkB,EAAE;MAAEC;IAAO,CAAC,CAAC;EAClD,CAAC;EAED;EACA6B,cAAcA,CAACC,IAAI,EAAE;IACnB,OAAOrC,KAAK,CAACM,GAAG,CAAC,oBAAoB+B,IAAI,EAAE,CAAC;EAC9C,CAAC;EAED;EACAC,sBAAsBA,CAACC,iBAAiB,EAAE;IACxC;IACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtC,MAAMC,GAAG,GAAG,2BAA2BJ,iBAAiB,OAAOC,SAAS,EAAE;IAE1EI,OAAO,CAACC,GAAG,CAAC,oBAAoBF,GAAG,EAAE,CAAC;IACtC,OAAO3C,KAAK,CAACM,GAAG,CAACqC,GAAG,CAAC;EACvB,CAAC;EAED;EACAG,oBAAoBA,CAACT,IAAI,EAAEE,iBAAiB,GAAG,IAAI,EAAE;IACnD;IACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;;IAEtC;IACA,IAAIC,GAAG,GAAGN,IAAI,CAACU,QAAQ,CAAC,GAAG,CAAC,GACxB,GAAGV,IAAI,OAAOG,SAAS,EAAE,GACzB,yBAAyBH,IAAI,OAAOG,SAAS,EAAE;;IAEnD;IACA,IAAID,iBAAiB,EAAE;MACrB;MACA,IAAIS,oBAAoB,GAAG,EAAE;MAE7B,IAAI,OAAOT,iBAAiB,KAAK,QAAQ,EAAE;QACzCK,OAAO,CAACC,GAAG,CAAC,uBAAuB,EAAEN,iBAAiB,CAAC;QACvD,IAAIA,iBAAiB,CAACU,kBAAkB,EAAE;UACxCD,oBAAoB,GAAGE,MAAM,CAACX,iBAAiB,CAACU,kBAAkB,CAAC;UACnEL,OAAO,CAACC,GAAG,CAAC,eAAeG,oBAAoB,EAAE,CAAC;QACpD,CAAC,MAAM;UACLJ,OAAO,CAACO,IAAI,CAAC,wCAAwC,CAAC;UACtD;UACAH,oBAAoB,GAAG,EAAE;QAC3B;MACF,CAAC,MAAM,IAAI,OAAOT,iBAAiB,KAAK,QAAQ,IAAIA,iBAAiB,CAACa,IAAI,CAAC,CAAC,EAAE;QAC5E;QACA,IAAIb,iBAAiB,CAACc,UAAU,CAAC,KAAK,CAAC,IAAId,iBAAiB,CAACe,KAAK,CAAC,cAAc,CAAC,EAAE;UAClFN,oBAAoB,GAAGT,iBAAiB,CAACa,IAAI,CAAC,CAAC;UAC/CR,OAAO,CAACC,GAAG,CAAC,iBAAiBG,oBAAoB,EAAE,CAAC;QACtD,CAAC,MAAM;UACLJ,OAAO,CAACO,IAAI,CAAC,iBAAiBZ,iBAAiB,EAAE,CAAC;UAClDS,oBAAoB,GAAG,EAAE;QAC3B;MACF,CAAC,MAAM;QACLJ,OAAO,CAACO,IAAI,CAAC,gBAAgB,OAAOZ,iBAAiB,QAAQA,iBAAiB,EAAE,CAAC;QACjFS,oBAAoB,GAAG,EAAE;MAC3B;MAEA,IAAIA,oBAAoB,EAAE;QACxBL,GAAG,IAAI,uBAAuBY,kBAAkB,CAACP,oBAAoB,CAAC,EAAE;QACxEJ,OAAO,CAACC,GAAG,CAAC,aAAaG,oBAAoB,EAAE,CAAC;MAClD;IACF;IAEAJ,OAAO,CAACC,GAAG,CAAC,cAAcF,GAAG,EAAE,CAAC;IAChC,OAAO3C,KAAK,CAACM,GAAG,CAACqC,GAAG,CAAC;EACvB,CAAC;EAED;EACAL,sBAAsBA,CAACC,iBAAiB,EAAE;IACxC;IACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtC,MAAMC,GAAG,GAAG,2BAA2BJ,iBAAiB,OAAOC,SAAS,EAAE;IAE1EI,OAAO,CAACC,GAAG,CAAC,oBAAoBF,GAAG,EAAE,CAAC;IACtC,OAAO3C,KAAK,CAACM,GAAG,CAACqC,GAAG,CAAC;EACvB,CAAC;EAED;EACAa,iBAAiBA,CAACnB,IAAI,EAAEhB,IAAI,EAAEkB,iBAAiB,GAAG,IAAI,EAAE;IACtD,IAAIA,iBAAiB,EAAE;MACrB;MACA,OAAOvC,KAAK,CAACwB,GAAG,CAAC,2BAA2Be,iBAAiB,EAAE,EAAElB,IAAI,CAAC;IACxE,CAAC,MAAM;MACL;MACA,OAAOrB,KAAK,CAACwB,GAAG,CAAC,yBAAyBa,IAAI,EAAE,EAAEhB,IAAI,CAAC;IACzD;EACF,CAAC;EAED;EACAoC,yBAAyBA,CAAClB,iBAAiB,EAAElB,IAAI,EAAE;IACjD,OAAOrB,KAAK,CAACwB,GAAG,CAAC,2BAA2Be,iBAAiB,EAAE,EAAElB,IAAI,CAAC;EACxE,CAAC;EAED;EACAqC,uBAAuBA,CAACrB,IAAI,EAAEhB,IAAI,EAAEkB,iBAAiB,GAAG,IAAI,EAAE;IAC5D,MAAMhC,MAAM,GAAGgC,iBAAiB,GAAG;MAAEU,kBAAkB,EAAEV;IAAkB,CAAC,GAAG,CAAC,CAAC;IACjF,OAAOvC,KAAK,CAACwB,GAAG,CAAC,yBAAyBa,IAAI,EAAE,EAAEhB,IAAI,EAAE;MAAEd;IAAO,CAAC,CAAC;EACrE,CAAC;EAED;EACAoD,8BAA8BA,CAACtB,IAAI,EAAE9B,MAAM,GAAG,CAAC,CAAC,EAAE;IAChD;IACA,MAAMiC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtC,MAAMkB,WAAW,GAAG;MAAE,GAAGrD,MAAM;MAAEsD,EAAE,EAAErB;IAAU,CAAC;IAEhDI,OAAO,CAACC,GAAG,CAAC,qBAAqBR,IAAI,EAAE,EAAEuB,WAAW,CAAC;IACrD,OAAO5D,KAAK,CAACM,GAAG,CAAC,yBAAyB+B,IAAI,EAAE,EAAE;MAAE9B,MAAM,EAAEqD;IAAY,CAAC,CAAC;EAC5E,CAAC;EAED;EACAE,iBAAiBA,CAACzB,IAAI,EAAEhB,IAAI,EAAE;IAC5B,OAAOrB,KAAK,CAACsB,IAAI,CAAC,gCAAgCe,IAAI,EAAE,EAAEhB,IAAI,CAAC;EACjE,CAAC;EAED;EACA0C,oBAAoBA,CAAC1B,IAAI,EAAE;IACzB,OAAOrC,KAAK,CAACM,GAAG,CAAC,2BAA2B+B,IAAI,EAAE,CAAC;EACrD,CAAC;EAED;EACA2B,qBAAqBA,CAAC3B,IAAI,EAAEE,iBAAiB,GAAG,IAAI,EAAE;IACpD;IACA,MAAMC,SAAS,GAAG,IAAIC,IAAI,CAAC,CAAC,CAACC,OAAO,CAAC,CAAC;IACtC,IAAIC,GAAG,GAAG,yBAAyBN,IAAI,eAAeG,SAAS,EAAE;;IAEjE;IACA,IAAID,iBAAiB,EAAE;MACrBI,GAAG,IAAI,uBAAuBY,kBAAkB,CAAChB,iBAAiB,CAAC,EAAE;IACvE;IAEA,OAAOvC,KAAK,CAACM,GAAG,CAACqC,GAAG,CAAC;EACvB,CAAC;EAED;EACAsB,kBAAkBA,CAACC,UAAU,EAAE;IAC7B,OAAOlE,KAAK,CAACsB,IAAI,CAAC,yBAAyB,EAAE4C,UAAU,EAAE;MACvDC,YAAY,EAAE,MAAM,CAAC;IACvB,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAMC,uBAAuB,GAAG;EACrC;EACAC,0BAA0BA,CAAChD,IAAI,EAAE;IAC/B,OAAOrB,KAAK,CAACsB,IAAI,CAAC,4BAA4B,EAAED,IAAI,CAAC;EACvD,CAAC;EAED;EACAiD,wBAAwBA,CAAC/D,MAAM,EAAE;IAC/B,OAAOP,KAAK,CAACM,GAAG,CAAC,4BAA4B,EAAE;MAAEC;IAAO,CAAC,CAAC;EAC5D,CAAC;EAED;EACAgE,uBAAuBA,CAAC1D,EAAE,EAAE;IAC1B,OAAOb,KAAK,CAACM,GAAG,CAAC,8BAA8BO,EAAE,EAAE,CAAC;EACtD,CAAC;EAED;EACA2D,6BAA6BA,CAACnC,IAAI,EAAE;IAClC,OAAOrC,KAAK,CAACM,GAAG,CAAC,mCAAmC+B,IAAI,EAAE,CAAC;EAC7D,CAAC;EAED;EACAoC,0BAA0BA,CAAC5D,EAAE,EAAEQ,IAAI,EAAEqD,gBAAgB,GAAG,CAAC,EAAE;IACzD,OAAO1E,KAAK,CAACwB,GAAG,CAAC,8BAA8BX,EAAE,EAAE,EAAEQ,IAAI,EAAE;MACzDd,MAAM,EAAE;QAAEoE,kBAAkB,EAAED;MAAiB;IACjD,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,0BAA0BA,CAAC/D,EAAE,EAAEgE,SAAS,GAAG,IAAI,EAAEC,IAAI,GAAG,OAAO,EAAE;IAC/D,OAAO9E,KAAK,CAACsB,IAAI,CAAC,qCAAqCT,EAAE,EAAE,EAAE,IAAI,EAAE;MACjEN,MAAM,EAAE;QACNwE,UAAU,EAAEF,SAAS;QACrBC,IAAI,EAAEA;MACR;IACF,CAAC,CAAC;EACJ,CAAC;EAED;EACAE,oBAAoBA,CAACnE,EAAE,EAAEoE,WAAW,GAAG,CAAC,EAAE;IACxC,OAAOjF,KAAK,CAACM,GAAG,CAAC,8BAA8BO,EAAE,eAAe,EAAE;MAChEN,MAAM,EAAE;QAAE2E,YAAY,EAAED;MAAY;IACtC,CAAC,CAAC;EACJ;AACF,CAAC;;AAED;AACA,OAAO,MAAME,QAAQ,GAAG;EACtB;EACAC,KAAKA,CAACC,QAAQ,EAAEC,QAAQ,EAAE;IACxB,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;IAC/BD,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEJ,QAAQ,CAAC;IACrCE,QAAQ,CAACE,MAAM,CAAC,UAAU,EAAEH,QAAQ,CAAC;IACrC,OAAOtF,KAAK,CAACsB,IAAI,CAAC,kBAAkB,EAAEiE,QAAQ,CAAC;EACjD,CAAC;EAED;EACAG,SAASA,CAACnF,MAAM,EAAE;IAChB,OAAOP,KAAK,CAACM,GAAG,CAAC,YAAY,EAAE;MAAEC;IAAO,CAAC,CAAC;EAC5C,CAAC;EAED;EACAoF,WAAWA,CAACtE,IAAI,EAAE;IAChB,OAAOrB,KAAK,CAACsB,IAAI,CAAC,YAAY,EAAED,IAAI,CAAC;EACvC,CAAC;EAED;EACAuE,WAAWA,CAAC/E,EAAE,EAAEQ,IAAI,EAAE;IACpB,OAAOrB,KAAK,CAACwB,GAAG,CAAC,cAAcX,EAAE,EAAE,EAAEQ,IAAI,CAAC;EAC5C,CAAC;EAED;EACAwE,WAAWA,CAAChF,EAAE,EAAE;IACd,OAAOb,KAAK,CAAC0B,MAAM,CAAC,cAAcb,EAAE,EAAE,CAAC;EACzC,CAAC;EAED;EACAiF,WAAWA,CAAA,EAAG;IACZ,OAAO9F,KAAK,CAACM,GAAG,CAAC,qBAAqB,CAAC;EACzC,CAAC;EAED;EACAyF,cAAcA,CAAC1E,IAAI,EAAE;IACnB,OAAOrB,KAAK,CAACwB,GAAG,CAAC,qBAAqB,EAAEH,IAAI,CAAC;EAC/C;AACF,CAAC;;AAED;AACA,OAAO,MAAM2E,OAAO,GAAG;EACrB;EACAZ,KAAKA,CAACa,WAAW,EAAE;IACjB,OAAOjG,KAAK,CAACsB,IAAI,CAAC,iBAAiB,EAAE2E,WAAW,CAAC;EACnD,CAAC;EAED;EACAC,UAAUA,CAAA,EAAG;IACX,OAAOlG,KAAK,CAACM,GAAG,CAAC,mBAAmB,CAAC;EACvC;AACF,CAAC;;AAED;AACA,OAAO,MAAM6F,eAAe,GAAG;EAC7B;EACAC,gBAAgBA,CAAC7F,MAAM,EAAE;IACvB,OAAOP,KAAK,CAACM,GAAG,CAAC,oBAAoB,EAAE;MAAEC;IAAO,CAAC,CAAC;EACpD,CAAC;EAED;EACA8F,eAAeA,CAACxF,EAAE,EAAE;IAClB,OAAOb,KAAK,CAACM,GAAG,CAAC,sBAAsBO,EAAE,EAAE,CAAC;EAC9C,CAAC;EAED;EACAyF,kBAAkBA,CAACjF,IAAI,EAAE;IACvB,OAAOrB,KAAK,CAACsB,IAAI,CAAC,oBAAoB,EAAED,IAAI,CAAC;EAC/C,CAAC;EAED;EACAkF,kBAAkBA,CAAC1F,EAAE,EAAEQ,IAAI,EAAE;IAC3B,OAAOrB,KAAK,CAACwB,GAAG,CAAC,sBAAsBX,EAAE,EAAE,EAAEQ,IAAI,CAAC;EACpD,CAAC;EAED;EACAmF,kBAAkBA,CAAC3F,EAAE,EAAE;IACrB,OAAOb,KAAK,CAAC0B,MAAM,CAAC,sBAAsBb,EAAE,EAAE,CAAC;EACjD;AACF,CAAC;;AAED;AACA,OAAO,MAAM4F,GAAG,GAAG;EACjBC,SAAS,EAAEhG,YAAY;EACvBiG,WAAW,EAAE1E,cAAc;EAC3B2E,oBAAoB,EAAExC,uBAAuB;EAC7CyC,QAAQ,EAAElF,WAAW;EACrBmF,IAAI,EAAEd,OAAO;EACbe,KAAK,EAAE5B,QAAQ;EACf6B,YAAY,EAAEb;AAChB,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}