2025-05-29 08:49:20,481 - root - INFO - 设备预定系统启动中...
2025-05-29 08:49:20,952 - root - INFO - FastAPI应用初始化中...
2025-05-29 08:49:20,955 - backend.i18n - INFO - 国际化设置成功
2025-05-29 08:49:21,651 - root - INFO - 应用启动 / Application started
2025-05-29 08:49:21,658 - backend.database - INFO - 数据库初始化成功
2025-05-29 08:49:21,658 - root - INFO - 后台任务已启动
2025-05-29 08:49:21,659 - root - INFO - 执行预约状态更新任务
2025-05-29 08:49:21,659 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:49:21.659816
2025-05-29 08:49:21,660 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=49, 秒=21
2025-05-29 08:49:21,680 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:49:21,681 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:49:21.659816 AND Reservation.end_datetime > 2025-05-29 08:49:21.659816
2025-05-29 08:49:21,684 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:49:21,684 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:49:21,684 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:49:21.659816
2025-05-29 08:49:21,686 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:49:21,686 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:49:21,687 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:50:10,578 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:50:19,088 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:50:21,689 - root - INFO - 执行预约状态更新任务
2025-05-29 08:50:21,689 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:50:21.689711
2025-05-29 08:50:21,690 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=50, 秒=21
2025-05-29 08:50:21,691 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:50:21,691 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:50:21.689711 AND Reservation.end_datetime > 2025-05-29 08:50:21.689711
2025-05-29 08:50:21,692 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:50:21,692 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:50:21,692 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:50:21.689711
2025-05-29 08:50:21,693 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:50:21,693 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:50:21,694 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:50:22,284 - backend.routes.reservation - INFO - 获取预约详情: 预约码=WM51NAWY, 开始时间=None, 结束时间=None, 预约序号=RN-20250623-9315-6
2025-05-29 08:50:22,285 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250623-9315-6，优先使用预约序号查询
2025-05-29 08:50:22,285 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250623-9315-6
2025-05-29 08:50:22,286 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=233, 状态=cancelled
2025-05-29 08:50:22,287 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=233, 状态=cancelled
2025-05-29 08:50:29,843 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:51:21,702 - root - INFO - 执行预约状态更新任务
2025-05-29 08:51:21,703 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:51:21.703300
2025-05-29 08:51:21,703 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=51, 秒=21
2025-05-29 08:51:21,704 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:51:21,704 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:51:21.703300 AND Reservation.end_datetime > 2025-05-29 08:51:21.703300
2025-05-29 08:51:21,705 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:51:21,705 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:51:21,706 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:51:21.703300
2025-05-29 08:51:21,706 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:51:21,707 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:51:21,707 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:52:21,709 - root - INFO - 执行预约状态更新任务
2025-05-29 08:52:21,709 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:52:21.709595
2025-05-29 08:52:21,710 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=52, 秒=21
2025-05-29 08:52:21,711 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:52:21,711 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:52:21.709595 AND Reservation.end_datetime > 2025-05-29 08:52:21.709595
2025-05-29 08:52:21,712 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:52:21,712 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:52:21,713 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:52:21.709595
2025-05-29 08:52:21,713 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:52:21,714 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:52:21,714 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:52:56,468 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:52:59,131 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 08:53:01,547 - backend.routes.reservation - INFO - 获取预约详情: 预约码=WM51NAWY, 开始时间=None, 结束时间=None, 预约序号=RN-20250623-9315-6
2025-05-29 08:53:01,547 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250623-9315-6，优先使用预约序号查询
2025-05-29 08:53:01,548 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250623-9315-6
2025-05-29 08:53:01,549 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=233, 状态=cancelled
2025-05-29 08:53:01,550 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=233, 状态=cancelled
2025-05-29 08:53:21,727 - root - INFO - 执行预约状态更新任务
2025-05-29 08:53:21,728 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:53:21.728218
2025-05-29 08:53:21,728 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=53, 秒=21
2025-05-29 08:53:21,729 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:53:21,729 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:53:21.728218 AND Reservation.end_datetime > 2025-05-29 08:53:21.728218
2025-05-29 08:53:21,730 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:53:21,731 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:53:21,731 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:53:21.728218
2025-05-29 08:53:21,731 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:53:21,732 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:53:21,732 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:54:21,735 - root - INFO - 执行预约状态更新任务
2025-05-29 08:54:21,736 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:54:21.736090
2025-05-29 08:54:21,736 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=54, 秒=21
2025-05-29 08:54:21,737 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:54:21,737 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:54:21.736090 AND Reservation.end_datetime > 2025-05-29 08:54:21.736090
2025-05-29 08:54:21,738 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:54:21,739 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:54:21,739 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:54:21.736090
2025-05-29 08:54:21,740 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:54:21,740 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:54:21,740 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:55:21,756 - root - INFO - 执行预约状态更新任务
2025-05-29 08:55:21,757 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:55:21.757309
2025-05-29 08:55:21,757 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=55, 秒=21
2025-05-29 08:55:21,758 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:55:21,758 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:55:21.757309 AND Reservation.end_datetime > 2025-05-29 08:55:21.757309
2025-05-29 08:55:21,759 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:55:21,760 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:55:21,760 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:55:21.757309
2025-05-29 08:55:21,761 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:55:21,761 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:55:21,761 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:56:21,770 - root - INFO - 执行预约状态更新任务
2025-05-29 08:56:21,771 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:56:21.771396
2025-05-29 08:56:21,772 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=56, 秒=21
2025-05-29 08:56:21,773 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:56:21,773 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:56:21.771396 AND Reservation.end_datetime > 2025-05-29 08:56:21.771396
2025-05-29 08:56:21,774 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:56:21,774 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:56:21,774 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:56:21.771396
2025-05-29 08:56:21,775 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:56:21,775 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:56:21,776 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:57:21,787 - root - INFO - 执行预约状态更新任务
2025-05-29 08:57:21,788 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:57:21.788490
2025-05-29 08:57:21,789 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=57, 秒=21
2025-05-29 08:57:21,790 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:57:21,790 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:57:21.788490 AND Reservation.end_datetime > 2025-05-29 08:57:21.788490
2025-05-29 08:57:21,791 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:57:21,792 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:57:21,792 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:57:21.788490
2025-05-29 08:57:21,793 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:57:21,794 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:57:21,795 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:58:21,798 - root - INFO - 执行预约状态更新任务
2025-05-29 08:58:21,798 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:58:21.798776
2025-05-29 08:58:21,799 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=58, 秒=21
2025-05-29 08:58:21,800 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:58:21,800 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:58:21.798776 AND Reservation.end_datetime > 2025-05-29 08:58:21.798776
2025-05-29 08:58:21,801 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:58:21,801 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:58:21,801 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:58:21.798776
2025-05-29 08:58:21,802 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:58:21,802 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:58:21,802 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 08:59:21,808 - root - INFO - 执行预约状态更新任务
2025-05-29 08:59:21,808 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 08:59:21.808842
2025-05-29 08:59:21,809 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=8, 分=59, 秒=21
2025-05-29 08:59:21,809 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 08:59:21,810 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 08:59:21.808842 AND Reservation.end_datetime > 2025-05-29 08:59:21.808842
2025-05-29 08:59:21,810 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 08:59:21,811 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 08:59:21,811 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 08:59:21.808842
2025-05-29 08:59:21,812 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 08:59:21,812 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 08:59:21,812 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:00:21,817 - root - INFO - 执行预约状态更新任务
2025-05-29 09:00:21,818 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:00:21.818007
2025-05-29 09:00:21,818 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=0, 秒=21
2025-05-29 09:00:21,819 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:00:21,819 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:00:21.818007 AND Reservation.end_datetime > 2025-05-29 09:00:21.818007
2025-05-29 09:00:21,820 - backend.utils.status_updater - INFO - 找到 1 个应该更新为'使用中'的预约
2025-05-29 09:00:21,820 - backend.utils.status_updater - INFO - 预约ID: 179, 开始时间: 2025-05-29 09:00:00, 结束时间: 2025-05-29 17:00:00, 当前状态: confirmed
2025-05-29 09:00:21,821 - backend.utils.status_updater - INFO - 预约ID: 179, 当前时间与开始时间的差值(秒): 21.818007, 结束时间与当前时间的差值(秒): 28778.181993
2025-05-29 09:00:21,821 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:00:21,821 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:00:21.818007
2025-05-29 09:00:21,822 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:00:21,824 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=1条, 已过期=0条
2025-05-29 09:00:21,826 - backend.utils.status_updater - INFO - 已更新预约状态: 1个更新为'使用中', 0个更新为'已过期'
2025-05-29 09:01:21,840 - root - INFO - 执行预约状态更新任务
2025-05-29 09:01:21,840 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:01:21.840848
2025-05-29 09:01:21,841 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=1, 秒=21
2025-05-29 09:01:21,842 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:01:21,842 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:01:21.840848 AND Reservation.end_datetime > 2025-05-29 09:01:21.840848
2025-05-29 09:01:21,843 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:01:21,843 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:01:21,843 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:01:21.840848
2025-05-29 09:01:21,844 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:01:21,844 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:01:21,844 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:02:21,860 - root - INFO - 执行预约状态更新任务
2025-05-29 09:02:21,861 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:02:21.861194
2025-05-29 09:02:21,862 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=2, 秒=21
2025-05-29 09:02:21,864 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:02:21,864 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:02:21.861194 AND Reservation.end_datetime > 2025-05-29 09:02:21.861194
2025-05-29 09:02:21,865 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:02:21,865 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:02:21,866 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:02:21.861194
2025-05-29 09:02:21,867 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:02:21,867 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:02:21,868 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:03:21,877 - root - INFO - 执行预约状态更新任务
2025-05-29 09:03:21,877 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:03:21.877743
2025-05-29 09:03:21,878 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=3, 秒=21
2025-05-29 09:03:21,879 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:03:21,879 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:03:21.877743 AND Reservation.end_datetime > 2025-05-29 09:03:21.877743
2025-05-29 09:03:21,880 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:03:21,880 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:03:21,880 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:03:21.877743
2025-05-29 09:03:21,881 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:03:21,882 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:03:21,882 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:04:21,893 - root - INFO - 执行预约状态更新任务
2025-05-29 09:04:21,893 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:04:21.893948
2025-05-29 09:04:21,894 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=4, 秒=21
2025-05-29 09:04:21,895 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:04:21,895 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:04:21.893948 AND Reservation.end_datetime > 2025-05-29 09:04:21.893948
2025-05-29 09:04:21,896 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:04:21,896 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:04:21,896 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:04:21.893948
2025-05-29 09:04:21,897 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:04:21,897 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:04:21,898 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:05:21,912 - root - INFO - 执行预约状态更新任务
2025-05-29 09:05:21,912 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:05:21.912800
2025-05-29 09:05:21,913 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=5, 秒=21
2025-05-29 09:05:21,914 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:05:21,914 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:05:21.912800 AND Reservation.end_datetime > 2025-05-29 09:05:21.912800
2025-05-29 09:05:21,915 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:05:21,915 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:05:21,915 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:05:21.912800
2025-05-29 09:05:21,916 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:05:21,916 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:05:21,917 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:06:21,930 - root - INFO - 执行预约状态更新任务
2025-05-29 09:06:21,930 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:06:21.930963
2025-05-29 09:06:21,931 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=6, 秒=21
2025-05-29 09:06:21,932 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:06:21,932 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:06:21.930963 AND Reservation.end_datetime > 2025-05-29 09:06:21.930963
2025-05-29 09:06:21,933 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:06:21,933 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:06:21,933 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:06:21.930963
2025-05-29 09:06:21,934 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:06:21,934 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:06:21,934 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:07:21,938 - root - INFO - 执行预约状态更新任务
2025-05-29 09:07:21,938 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:07:21.938586
2025-05-29 09:07:21,939 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=7, 秒=21
2025-05-29 09:07:21,939 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:07:21,940 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:07:21.938586 AND Reservation.end_datetime > 2025-05-29 09:07:21.938586
2025-05-29 09:07:21,941 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:07:21,941 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:07:21,941 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:07:21.938586
2025-05-29 09:07:21,942 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:07:21,942 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:07:21,942 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:08:21,950 - root - INFO - 执行预约状态更新任务
2025-05-29 09:08:21,950 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:08:21.950725
2025-05-29 09:08:21,951 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=8, 秒=21
2025-05-29 09:08:21,952 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:08:21,952 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:08:21.950725 AND Reservation.end_datetime > 2025-05-29 09:08:21.950725
2025-05-29 09:08:21,953 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:08:21,953 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:08:21,953 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:08:21.950725
2025-05-29 09:08:21,954 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:08:21,954 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:08:21,955 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:09:21,959 - root - INFO - 执行预约状态更新任务
2025-05-29 09:09:21,960 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:09:21.960416
2025-05-29 09:09:21,960 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=9, 秒=21
2025-05-29 09:09:21,961 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:09:21,962 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:09:21.960416 AND Reservation.end_datetime > 2025-05-29 09:09:21.960416
2025-05-29 09:09:21,963 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:09:21,963 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:09:21,963 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:09:21.960416
2025-05-29 09:09:21,964 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:09:21,964 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:09:21,964 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:10:21,973 - root - INFO - 执行预约状态更新任务
2025-05-29 09:10:21,973 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:10:21.973590
2025-05-29 09:10:21,974 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=10, 秒=21
2025-05-29 09:10:21,974 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:10:21,975 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:10:21.973590 AND Reservation.end_datetime > 2025-05-29 09:10:21.973590
2025-05-29 09:10:21,976 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:10:21,976 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:10:21,977 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:10:21.973590
2025-05-29 09:10:21,977 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:10:21,978 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:10:21,978 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:11:21,989 - root - INFO - 执行预约状态更新任务
2025-05-29 09:11:21,990 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:11:21.990209
2025-05-29 09:11:21,990 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=11, 秒=21
2025-05-29 09:11:21,991 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:11:21,991 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:11:21.990209 AND Reservation.end_datetime > 2025-05-29 09:11:21.990209
2025-05-29 09:11:21,992 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:11:21,992 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:11:21,993 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:11:21.990209
2025-05-29 09:11:21,993 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:11:21,994 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:11:21,994 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:12:22,010 - root - INFO - 执行预约状态更新任务
2025-05-29 09:12:22,010 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:12:22.010878
2025-05-29 09:12:22,011 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=12, 秒=22
2025-05-29 09:12:22,012 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:12:22,012 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:12:22.010878 AND Reservation.end_datetime > 2025-05-29 09:12:22.010878
2025-05-29 09:12:22,013 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:12:22,013 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:12:22,014 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:12:22.010878
2025-05-29 09:12:22,014 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:12:22,015 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:12:22,015 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:13:22,031 - root - INFO - 执行预约状态更新任务
2025-05-29 09:13:22,031 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:13:22.031545
2025-05-29 09:13:22,031 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=13, 秒=22
2025-05-29 09:13:22,032 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:13:22,033 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:13:22.031545 AND Reservation.end_datetime > 2025-05-29 09:13:22.031545
2025-05-29 09:13:22,033 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:13:22,034 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:13:22,034 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:13:22.031545
2025-05-29 09:13:22,035 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:13:22,035 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:13:22,035 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:14:22,046 - root - INFO - 执行预约状态更新任务
2025-05-29 09:14:22,046 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:14:22.046842
2025-05-29 09:14:22,047 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=14, 秒=22
2025-05-29 09:14:22,048 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:14:22,048 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:14:22.046842 AND Reservation.end_datetime > 2025-05-29 09:14:22.046842
2025-05-29 09:14:22,049 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:14:22,049 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:14:22,049 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:14:22.046842
2025-05-29 09:14:22,050 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:14:22,050 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:14:22,051 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:15:22,064 - root - INFO - 执行预约状态更新任务
2025-05-29 09:15:22,065 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:15:22.065504
2025-05-29 09:15:22,065 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=15, 秒=22
2025-05-29 09:15:22,066 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:15:22,067 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:15:22.065504 AND Reservation.end_datetime > 2025-05-29 09:15:22.065504
2025-05-29 09:15:22,068 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:15:22,068 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:15:22,068 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:15:22.065504
2025-05-29 09:15:22,069 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:15:22,069 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:15:22,069 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:16:22,083 - root - INFO - 执行预约状态更新任务
2025-05-29 09:16:22,083 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:16:22.083864
2025-05-29 09:16:22,084 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=16, 秒=22
2025-05-29 09:16:22,085 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:16:22,086 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:16:22.083864 AND Reservation.end_datetime > 2025-05-29 09:16:22.083864
2025-05-29 09:16:22,087 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:16:22,087 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:16:22,087 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:16:22.083864
2025-05-29 09:16:22,088 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:16:22,088 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:16:22,089 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:17:22,092 - root - INFO - 执行预约状态更新任务
2025-05-29 09:17:22,093 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:17:22.093364
2025-05-29 09:17:22,093 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=17, 秒=22
2025-05-29 09:17:22,094 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:17:22,095 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:17:22.093364 AND Reservation.end_datetime > 2025-05-29 09:17:22.093364
2025-05-29 09:17:22,095 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:17:22,096 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:17:22,096 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:17:22.093364
2025-05-29 09:17:22,097 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:17:22,098 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:17:22,098 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:18:22,103 - root - INFO - 执行预约状态更新任务
2025-05-29 09:18:22,104 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:18:22.104187
2025-05-29 09:18:22,104 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=18, 秒=22
2025-05-29 09:18:22,105 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:18:22,105 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:18:22.104187 AND Reservation.end_datetime > 2025-05-29 09:18:22.104187
2025-05-29 09:18:22,106 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:18:22,106 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:18:22,107 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:18:22.104187
2025-05-29 09:18:22,107 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:18:22,108 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:18:22,108 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:19:22,109 - root - INFO - 执行预约状态更新任务
2025-05-29 09:19:22,109 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:19:22.109525
2025-05-29 09:19:22,109 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=19, 秒=22
2025-05-29 09:19:22,110 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:19:22,111 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:19:22.109525 AND Reservation.end_datetime > 2025-05-29 09:19:22.109525
2025-05-29 09:19:22,111 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:19:22,112 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:19:22,112 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:19:22.109525
2025-05-29 09:19:22,113 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:19:22,113 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:19:22,113 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:20:22,114 - root - INFO - 执行预约状态更新任务
2025-05-29 09:20:22,114 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:20:22.114601
2025-05-29 09:20:22,115 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=20, 秒=22
2025-05-29 09:20:22,116 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:20:22,116 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:20:22.114601 AND Reservation.end_datetime > 2025-05-29 09:20:22.114601
2025-05-29 09:20:22,117 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:20:22,117 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:20:22,117 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:20:22.114601
2025-05-29 09:20:22,118 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:20:22,118 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:20:22,118 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:21:22,121 - root - INFO - 执行预约状态更新任务
2025-05-29 09:21:22,121 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:21:22.121656
2025-05-29 09:21:22,122 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=21, 秒=22
2025-05-29 09:21:22,122 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:21:22,123 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:21:22.121656 AND Reservation.end_datetime > 2025-05-29 09:21:22.121656
2025-05-29 09:21:22,124 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:21:22,124 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:21:22,124 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:21:22.121656
2025-05-29 09:21:22,125 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:21:22,125 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:21:22,125 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:22:22,136 - root - INFO - 执行预约状态更新任务
2025-05-29 09:22:22,137 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:22:22.137280
2025-05-29 09:22:22,137 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=22, 秒=22
2025-05-29 09:22:22,138 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:22:22,138 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:22:22.137280 AND Reservation.end_datetime > 2025-05-29 09:22:22.137280
2025-05-29 09:22:22,139 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:22:22,139 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:22:22,139 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:22:22.137280
2025-05-29 09:22:22,140 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:22:22,140 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:22:22,140 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:23:22,141 - root - INFO - 执行预约状态更新任务
2025-05-29 09:23:22,142 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:23:22.142201
2025-05-29 09:23:22,142 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=23, 秒=22
2025-05-29 09:23:22,143 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:23:22,143 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:23:22.142201 AND Reservation.end_datetime > 2025-05-29 09:23:22.142201
2025-05-29 09:23:22,144 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:23:22,144 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:23:22,145 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:23:22.142201
2025-05-29 09:23:22,145 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:23:22,146 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:23:22,146 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:24:22,147 - root - INFO - 执行预约状态更新任务
2025-05-29 09:24:22,147 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:24:22.147693
2025-05-29 09:24:22,148 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=24, 秒=22
2025-05-29 09:24:22,148 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:24:22,149 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:24:22.147693 AND Reservation.end_datetime > 2025-05-29 09:24:22.147693
2025-05-29 09:24:22,150 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:24:22,150 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:24:22,150 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:24:22.147693
2025-05-29 09:24:22,151 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:24:22,151 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:24:22,152 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:25:22,156 - root - INFO - 执行预约状态更新任务
2025-05-29 09:25:22,157 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:25:22.156987
2025-05-29 09:25:22,157 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=25, 秒=22
2025-05-29 09:25:22,158 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:25:22,158 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:25:22.156987 AND Reservation.end_datetime > 2025-05-29 09:25:22.156987
2025-05-29 09:25:22,159 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:25:22,160 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:25:22,160 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:25:22.156987
2025-05-29 09:25:22,161 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:25:22,161 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:25:22,161 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:26:22,174 - root - INFO - 执行预约状态更新任务
2025-05-29 09:26:22,175 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:26:22.175233
2025-05-29 09:26:22,175 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=26, 秒=22
2025-05-29 09:26:22,176 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:26:22,176 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:26:22.175233 AND Reservation.end_datetime > 2025-05-29 09:26:22.175233
2025-05-29 09:26:22,177 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:26:22,177 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:26:22,178 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:26:22.175233
2025-05-29 09:26:22,178 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:26:22,178 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:26:22,179 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:27:22,184 - root - INFO - 执行预约状态更新任务
2025-05-29 09:27:22,184 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:27:22.184441
2025-05-29 09:27:22,184 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=27, 秒=22
2025-05-29 09:27:22,185 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:27:22,186 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:27:22.184441 AND Reservation.end_datetime > 2025-05-29 09:27:22.184441
2025-05-29 09:27:22,187 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:27:22,187 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:27:22,187 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:27:22.184441
2025-05-29 09:27:22,188 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:27:22,188 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:27:22,188 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:28:22,192 - root - INFO - 执行预约状态更新任务
2025-05-29 09:28:22,192 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:28:22.192968
2025-05-29 09:28:22,193 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=28, 秒=22
2025-05-29 09:28:22,194 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:28:22,194 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:28:22.192968 AND Reservation.end_datetime > 2025-05-29 09:28:22.192968
2025-05-29 09:28:22,195 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:28:22,195 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:28:22,195 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:28:22.192968
2025-05-29 09:28:22,196 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:28:22,196 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:28:22,197 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:29:22,202 - root - INFO - 执行预约状态更新任务
2025-05-29 09:29:22,202 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:29:22.202600
2025-05-29 09:29:22,202 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=29, 秒=22
2025-05-29 09:29:22,204 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:29:22,204 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:29:22.202600 AND Reservation.end_datetime > 2025-05-29 09:29:22.202600
2025-05-29 09:29:22,205 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:29:22,205 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:29:22,205 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:29:22.202600
2025-05-29 09:29:22,206 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:29:22,206 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:29:22,206 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:30:22,216 - root - INFO - 执行预约状态更新任务
2025-05-29 09:30:22,217 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:30:22.217076
2025-05-29 09:30:22,217 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=30, 秒=22
2025-05-29 09:30:22,218 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:30:22,218 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:30:22.217076 AND Reservation.end_datetime > 2025-05-29 09:30:22.217076
2025-05-29 09:30:22,219 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:30:22,220 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:30:22,220 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:30:22.217076
2025-05-29 09:30:22,221 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:30:22,221 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:30:22,221 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:31:22,233 - root - INFO - 执行预约状态更新任务
2025-05-29 09:31:22,233 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:31:22.233825
2025-05-29 09:31:22,234 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=31, 秒=22
2025-05-29 09:31:22,235 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:31:22,235 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:31:22.233825 AND Reservation.end_datetime > 2025-05-29 09:31:22.233825
2025-05-29 09:31:22,236 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:31:22,236 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:31:22,236 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:31:22.233825
2025-05-29 09:31:22,237 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:31:22,238 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:31:22,238 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:32:22,251 - root - INFO - 执行预约状态更新任务
2025-05-29 09:32:22,251 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:32:22.251545
2025-05-29 09:32:22,251 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=32, 秒=22
2025-05-29 09:32:22,252 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:32:22,253 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:32:22.251545 AND Reservation.end_datetime > 2025-05-29 09:32:22.251545
2025-05-29 09:32:22,254 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:32:22,254 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:32:22,254 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:32:22.251545
2025-05-29 09:32:22,255 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:32:22,255 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:32:22,255 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:33:22,269 - root - INFO - 执行预约状态更新任务
2025-05-29 09:33:22,270 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:33:22.270215
2025-05-29 09:33:22,270 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=33, 秒=22
2025-05-29 09:33:22,271 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:33:22,271 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:33:22.270215 AND Reservation.end_datetime > 2025-05-29 09:33:22.270215
2025-05-29 09:33:22,272 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:33:22,272 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:33:22,272 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:33:22.270215
2025-05-29 09:33:22,273 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:33:22,273 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:33:22,274 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:34:05,819 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:34:14,970 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:34:16,865 - backend.routes.reservation - INFO - 获取预约详情: 预约码=WM51NAWY, 开始时间=None, 结束时间=None, 预约序号=RN-20250620-9315-5
2025-05-29 09:34:16,865 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250620-9315-5，优先使用预约序号查询
2025-05-29 09:34:16,865 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250620-9315-5
2025-05-29 09:34:16,866 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=232, 状态=confirmed
2025-05-29 09:34:16,867 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=232, 状态=confirmed
2025-05-29 09:34:21,318 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:34:22,287 - root - INFO - 执行预约状态更新任务
2025-05-29 09:34:22,287 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:34:22.287973
2025-05-29 09:34:22,288 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=34, 秒=22
2025-05-29 09:34:22,289 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:34:22,289 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:34:22.287973 AND Reservation.end_datetime > 2025-05-29 09:34:22.287973
2025-05-29 09:34:22,290 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:34:22,290 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:34:22,291 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:34:22.287973
2025-05-29 09:34:22,291 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:34:22,291 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:34:22,292 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:35:22,293 - root - INFO - 执行预约状态更新任务
2025-05-29 09:35:22,294 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:35:22.294349
2025-05-29 09:35:22,294 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=35, 秒=22
2025-05-29 09:35:22,296 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:35:22,297 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:35:22.294349 AND Reservation.end_datetime > 2025-05-29 09:35:22.294349
2025-05-29 09:35:22,298 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:35:22,299 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:35:22,300 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:35:22.294349
2025-05-29 09:35:22,302 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:35:22,302 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:35:22,302 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:36:22,310 - root - INFO - 执行预约状态更新任务
2025-05-29 09:36:22,310 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:36:22.310946
2025-05-29 09:36:22,311 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=36, 秒=22
2025-05-29 09:36:22,312 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:36:22,312 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:36:22.310946 AND Reservation.end_datetime > 2025-05-29 09:36:22.310946
2025-05-29 09:36:22,313 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:36:22,313 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:36:22,313 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:36:22.310946
2025-05-29 09:36:22,314 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:36:22,314 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:36:22,315 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:37:22,322 - root - INFO - 执行预约状态更新任务
2025-05-29 09:37:22,323 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:37:22.323212
2025-05-29 09:37:22,323 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=37, 秒=22
2025-05-29 09:37:22,324 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:37:22,324 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:37:22.323212 AND Reservation.end_datetime > 2025-05-29 09:37:22.323212
2025-05-29 09:37:22,325 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:37:22,325 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:37:22,326 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:37:22.323212
2025-05-29 09:37:22,326 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:37:22,327 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:37:22,327 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:38:22,335 - root - INFO - 执行预约状态更新任务
2025-05-29 09:38:22,336 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:38:22.336216
2025-05-29 09:38:22,336 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=38, 秒=22
2025-05-29 09:38:22,337 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:38:22,337 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:38:22.336216 AND Reservation.end_datetime > 2025-05-29 09:38:22.336216
2025-05-29 09:38:22,338 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:38:22,338 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:38:22,339 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:38:22.336216
2025-05-29 09:38:22,339 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:38:22,340 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:38:22,340 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:39:22,343 - root - INFO - 执行预约状态更新任务
2025-05-29 09:39:22,343 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:39:22.343577
2025-05-29 09:39:22,344 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=39, 秒=22
2025-05-29 09:39:22,345 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:39:22,345 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:39:22.343577 AND Reservation.end_datetime > 2025-05-29 09:39:22.343577
2025-05-29 09:39:22,346 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:39:22,347 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:39:22,347 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:39:22.343577
2025-05-29 09:39:22,348 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:39:22,348 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:39:22,348 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:40:11,439 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:40:15,303 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:40:22,001 - backend.routes.reservation - INFO - 获取预约详情: 预约码=ZFEXH6FR, 开始时间=None, 结束时间=None, 预约序号=RN-20250623-9315-6
2025-05-29 09:40:22,002 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250623-9315-6，优先使用预约序号查询
2025-05-29 09:40:22,002 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250623-9315-6
2025-05-29 09:40:22,003 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=233, 状态=cancelled
2025-05-29 09:40:22,004 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=233, 状态=cancelled
2025-05-29 09:40:22,363 - root - INFO - 执行预约状态更新任务
2025-05-29 09:40:22,363 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:40:22.363915
2025-05-29 09:40:22,364 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=40, 秒=22
2025-05-29 09:40:22,365 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:40:22,365 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:40:22.363915 AND Reservation.end_datetime > 2025-05-29 09:40:22.363915
2025-05-29 09:40:22,366 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:40:22,366 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:40:22,366 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:40:22.363915
2025-05-29 09:40:22,367 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:40:22,367 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:40:22,367 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:40:33,442 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:41:22,376 - root - INFO - 执行预约状态更新任务
2025-05-29 09:41:22,377 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:41:22.377516
2025-05-29 09:41:22,377 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=41, 秒=22
2025-05-29 09:41:22,378 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:41:22,378 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:41:22.377516 AND Reservation.end_datetime > 2025-05-29 09:41:22.377516
2025-05-29 09:41:22,379 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:41:22,379 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:41:22,380 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:41:22.377516
2025-05-29 09:41:22,380 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:41:22,381 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:41:22,381 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:42:22,393 - root - INFO - 执行预约状态更新任务
2025-05-29 09:42:22,393 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:42:22.393881
2025-05-29 09:42:22,394 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=42, 秒=22
2025-05-29 09:42:22,395 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:42:22,395 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:42:22.393881 AND Reservation.end_datetime > 2025-05-29 09:42:22.393881
2025-05-29 09:42:22,396 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:42:22,396 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:42:22,396 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:42:22.393881
2025-05-29 09:42:22,397 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:42:22,397 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:42:22,397 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:43:22,406 - root - INFO - 执行预约状态更新任务
2025-05-29 09:43:22,406 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:43:22.406566
2025-05-29 09:43:22,407 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=43, 秒=22
2025-05-29 09:43:22,407 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:43:22,408 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:43:22.406566 AND Reservation.end_datetime > 2025-05-29 09:43:22.406566
2025-05-29 09:43:22,408 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:43:22,409 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:43:22,409 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:43:22.406566
2025-05-29 09:43:22,410 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:43:22,410 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:43:22,410 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:44:22,419 - root - INFO - 执行预约状态更新任务
2025-05-29 09:44:22,419 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:44:22.419823
2025-05-29 09:44:22,420 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=44, 秒=22
2025-05-29 09:44:22,421 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:44:22,421 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:44:22.419823 AND Reservation.end_datetime > 2025-05-29 09:44:22.419823
2025-05-29 09:44:22,422 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:44:22,422 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:44:22,422 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:44:22.419823
2025-05-29 09:44:22,423 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:44:22,423 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:44:22,423 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:45:21,076 - root - INFO - 执行预约状态更新任务
2025-05-29 09:45:21,077 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:45:21.076985
2025-05-29 09:45:21,077 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=45, 秒=21
2025-05-29 09:45:21,078 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:45:21,078 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:45:21.076985 AND Reservation.end_datetime > 2025-05-29 09:45:21.076985
2025-05-29 09:45:21,079 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:45:21,079 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:45:21,079 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:45:21.076985
2025-05-29 09:45:21,080 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:45:21,080 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:45:21,080 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:46:21,091 - root - INFO - 执行预约状态更新任务
2025-05-29 09:46:21,092 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:46:21.092372
2025-05-29 09:46:21,092 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=46, 秒=21
2025-05-29 09:46:21,093 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:46:21,093 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:46:21.092372 AND Reservation.end_datetime > 2025-05-29 09:46:21.092372
2025-05-29 09:46:21,094 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:46:21,094 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:46:21,094 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:46:21.092372
2025-05-29 09:46:21,095 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:46:21,095 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:46:21,095 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:47:21,108 - root - INFO - 执行预约状态更新任务
2025-05-29 09:47:21,108 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:47:21.108683
2025-05-29 09:47:21,109 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=47, 秒=21
2025-05-29 09:47:21,110 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:47:21,110 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:47:21.108683 AND Reservation.end_datetime > 2025-05-29 09:47:21.108683
2025-05-29 09:47:21,111 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:47:21,111 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:47:21,111 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:47:21.108683
2025-05-29 09:47:21,112 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:47:21,112 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:47:21,112 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:48:21,113 - root - INFO - 执行预约状态更新任务
2025-05-29 09:48:21,114 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:48:21.114185
2025-05-29 09:48:21,114 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=48, 秒=21
2025-05-29 09:48:21,115 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:48:21,115 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:48:21.114185 AND Reservation.end_datetime > 2025-05-29 09:48:21.114185
2025-05-29 09:48:21,116 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:48:21,116 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:48:21,117 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:48:21.114185
2025-05-29 09:48:21,117 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:48:21,118 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:48:21,118 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:49:20,342 - root - INFO - 执行日志维护任务
2025-05-29 09:49:20,344 - root - INFO - 日志备份已创建: logs\backups\app-20250529-094920.log
2025-05-29 09:49:20,344 - root - INFO - 执行预约序号重复检查任务
2025-05-29 09:49:20,344 - backend.utils.duplicate_checker - INFO - 开始检查重复的预约序号...
2025-05-29 09:49:20,345 - backend.utils.duplicate_checker - INFO - 没有发现重复的预约序号
2025-05-29 09:49:20,346 - root - INFO - 没有发现重复的预约序号
2025-05-29 09:49:21,126 - root - INFO - 执行预约状态更新任务
2025-05-29 09:49:21,126 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:49:21.126619
2025-05-29 09:49:21,126 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=49, 秒=21
2025-05-29 09:49:21,127 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:49:21,128 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:49:21.126619 AND Reservation.end_datetime > 2025-05-29 09:49:21.126619
2025-05-29 09:49:21,129 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:49:21,129 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:49:21,129 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:49:21.126619
2025-05-29 09:49:21,130 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:49:21,130 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:49:21,130 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:50:21,142 - root - INFO - 执行预约状态更新任务
2025-05-29 09:50:21,142 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:50:21.142695
2025-05-29 09:50:21,143 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=50, 秒=21
2025-05-29 09:50:21,143 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:50:21,144 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:50:21.142695 AND Reservation.end_datetime > 2025-05-29 09:50:21.142695
2025-05-29 09:50:21,145 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:50:21,145 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:50:21,145 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:50:21.142695
2025-05-29 09:50:21,146 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:50:21,146 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:50:21,146 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:51:21,157 - root - INFO - 执行预约状态更新任务
2025-05-29 09:51:21,158 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:51:21.158487
2025-05-29 09:51:21,158 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=51, 秒=21
2025-05-29 09:51:21,160 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:51:21,160 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:51:21.158487 AND Reservation.end_datetime > 2025-05-29 09:51:21.158487
2025-05-29 09:51:21,161 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:51:21,161 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:51:21,161 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:51:21.158487
2025-05-29 09:51:21,162 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:51:21,163 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:51:21,163 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:52:14,880 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 09:52:21,168 - root - INFO - 执行预约状态更新任务
2025-05-29 09:52:21,168 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:52:21.168763
2025-05-29 09:52:21,169 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=52, 秒=21
2025-05-29 09:52:21,169 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:52:21,170 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:52:21.168763 AND Reservation.end_datetime > 2025-05-29 09:52:21.168763
2025-05-29 09:52:21,170 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:52:21,171 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:52:21,171 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:52:21.168763
2025-05-29 09:52:21,171 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:52:21,172 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:52:21,172 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:53:21,176 - root - INFO - 执行预约状态更新任务
2025-05-29 09:53:21,176 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:53:21.176876
2025-05-29 09:53:21,177 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=53, 秒=21
2025-05-29 09:53:21,178 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:53:21,178 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:53:21.176876 AND Reservation.end_datetime > 2025-05-29 09:53:21.176876
2025-05-29 09:53:21,179 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:53:21,179 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:53:21,179 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:53:21.176876
2025-05-29 09:53:21,180 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:53:21,180 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:53:21,180 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:54:21,191 - root - INFO - 执行预约状态更新任务
2025-05-29 09:54:21,191 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:54:21.191863
2025-05-29 09:54:21,192 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=54, 秒=21
2025-05-29 09:54:21,193 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:54:21,193 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:54:21.191863 AND Reservation.end_datetime > 2025-05-29 09:54:21.191863
2025-05-29 09:54:21,194 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:54:21,194 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:54:21,194 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:54:21.191863
2025-05-29 09:54:21,195 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:54:21,195 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:54:21,195 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:55:21,203 - root - INFO - 执行预约状态更新任务
2025-05-29 09:55:21,204 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:55:21.204318
2025-05-29 09:55:21,204 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=55, 秒=21
2025-05-29 09:55:21,205 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:55:21,205 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:55:21.204318 AND Reservation.end_datetime > 2025-05-29 09:55:21.204318
2025-05-29 09:55:21,206 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:55:21,206 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:55:21,206 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:55:21.204318
2025-05-29 09:55:21,207 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:55:21,207 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:55:21,208 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:56:21,222 - root - INFO - 执行预约状态更新任务
2025-05-29 09:56:21,223 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:56:21.223506
2025-05-29 09:56:21,223 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=56, 秒=21
2025-05-29 09:56:21,224 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:56:21,225 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:56:21.223506 AND Reservation.end_datetime > 2025-05-29 09:56:21.223506
2025-05-29 09:56:21,226 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:56:21,226 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:56:21,226 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:56:21.223506
2025-05-29 09:56:21,227 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:56:21,227 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:56:21,227 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:57:21,229 - root - INFO - 执行预约状态更新任务
2025-05-29 09:57:21,229 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:57:21.229640
2025-05-29 09:57:21,230 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=57, 秒=21
2025-05-29 09:57:21,230 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:57:21,231 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:57:21.229640 AND Reservation.end_datetime > 2025-05-29 09:57:21.229640
2025-05-29 09:57:21,231 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:57:21,232 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:57:21,232 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:57:21.229640
2025-05-29 09:57:21,233 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:57:21,233 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:57:21,233 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:58:21,239 - root - INFO - 执行预约状态更新任务
2025-05-29 09:58:21,239 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:58:21.239661
2025-05-29 09:58:21,240 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=58, 秒=21
2025-05-29 09:58:21,240 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:58:21,241 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:58:21.239661 AND Reservation.end_datetime > 2025-05-29 09:58:21.239661
2025-05-29 09:58:21,241 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:58:21,242 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:58:21,242 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:58:21.239661
2025-05-29 09:58:21,243 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:58:21,243 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:58:21,243 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:59:21,250 - root - INFO - 执行预约状态更新任务
2025-05-29 09:59:21,251 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 09:59:21.251375
2025-05-29 09:59:21,251 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=9, 分=59, 秒=21
2025-05-29 09:59:21,252 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 09:59:21,253 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 09:59:21.251375 AND Reservation.end_datetime > 2025-05-29 09:59:21.251375
2025-05-29 09:59:21,253 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 09:59:21,254 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 09:59:21,254 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 09:59:21.251375
2025-05-29 09:59:21,255 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 09:59:21,255 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 09:59:21,255 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 09:59:23,854 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:21,264 - root - INFO - 执行预约状态更新任务
2025-05-29 10:00:21,264 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:00:21.264785
2025-05-29 10:00:21,265 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=0, 秒=21
2025-05-29 10:00:21,265 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:00:21,266 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:00:21.264785 AND Reservation.end_datetime > 2025-05-29 10:00:21.264785
2025-05-29 10:00:21,267 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:00:21,267 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:00:21,267 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:00:21.264785
2025-05-29 10:00:21,268 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:00:21,268 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:00:21,268 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:00:34,093 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:38,223 - backend.routes.reservation - INFO - 获取预约详情: 预约码=LM2VEHSC, 开始时间=None, 结束时间=None, 预约序号=RN-20250626-8071-22
2025-05-29 10:00:38,224 - backend.routes.reservation - INFO - 提供了预约序号 RN-20250626-8071-22，优先使用预约序号查询
2025-05-29 10:00:38,224 - backend.routes.reservation - INFO - 使用预约序号查询: RN-20250626-8071-22
2025-05-29 10:00:38,225 - backend.routes.reservation - INFO - 通过预约序号直接找到预约: ID=161, 状态=cancelled
2025-05-29 10:00:38,226 - backend.routes.reservation - INFO - 通过预约序号查询成功，返回预约详情: ID=161, 状态=cancelled
2025-05-29 10:00:42,839 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:46,814 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:50,903 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:00:53,876 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:01:08,569 - backend.routes.reservation - INFO - 排序参数: sort_by=None, sort_order=None
2025-05-29 10:01:21,274 - root - INFO - 执行预约状态更新任务
2025-05-29 10:01:21,274 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:01:21.274906
2025-05-29 10:01:21,275 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=1, 秒=21
2025-05-29 10:01:21,276 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:01:21,276 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:01:21.274906 AND Reservation.end_datetime > 2025-05-29 10:01:21.274906
2025-05-29 10:01:21,277 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:01:21,277 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:01:21,278 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:01:21.274906
2025-05-29 10:01:21,279 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:01:21,279 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:01:21,279 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:02:21,289 - root - INFO - 执行预约状态更新任务
2025-05-29 10:02:21,289 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:02:21.289839
2025-05-29 10:02:21,290 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=2, 秒=21
2025-05-29 10:02:21,291 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:02:21,291 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:02:21.289839 AND Reservation.end_datetime > 2025-05-29 10:02:21.289839
2025-05-29 10:02:21,292 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:02:21,292 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:02:21,292 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:02:21.289839
2025-05-29 10:02:21,293 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:02:21,293 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:02:21,293 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:02:46,157 - backend.routes.reservation - INFO - 获取预约详情: 预约码=L44YME4K, 开始时间=None, 结束时间=None, 预约序号=None
2025-05-29 10:02:46,159 - backend.routes.reservation - INFO - 尝试获取与预约码匹配的预约
2025-05-29 10:02:46,160 - backend.routes.reservation - INFO - 没有日期参数，直接查找最近的预约
2025-05-29 10:02:46,161 - backend.routes.reservation - INFO - 找到已确认的预约: ID=222
2025-05-29 10:02:46,161 - backend.routes.reservation - INFO - 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 10:02:46,197 - backend.routes.reservation - INFO - 获取预约详情: 预约码=L44YME4K, 开始时间=None, 结束时间=None, 预约序号=None
2025-05-29 10:02:46,198 - backend.routes.reservation - INFO - 尝试获取与预约码匹配的预约
2025-05-29 10:02:46,198 - backend.routes.reservation - INFO - 没有日期参数，直接查找最近的预约
2025-05-29 10:02:46,199 - backend.routes.reservation - INFO - 找到已确认的预约: ID=222
2025-05-29 10:02:46,200 - backend.routes.reservation - INFO - 成功获取预约: ID=222, 状态=confirmed, 开始时间=2025-06-03 02:00:00, 结束时间=2025-06-03 09:00:00, 预约序号=RN-20250516-8875
2025-05-29 10:03:21,299 - root - INFO - 执行预约状态更新任务
2025-05-29 10:03:21,300 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:03:21.300380
2025-05-29 10:03:21,301 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=3, 秒=21
2025-05-29 10:03:21,303 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:03:21,304 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:03:21.300380 AND Reservation.end_datetime > 2025-05-29 10:03:21.300380
2025-05-29 10:03:21,306 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:03:21,306 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:03:21,307 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:03:21.300380
2025-05-29 10:03:21,308 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:03:21,308 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:03:21,309 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:04:21,324 - root - INFO - 执行预约状态更新任务
2025-05-29 10:04:21,325 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:04:21.325038
2025-05-29 10:04:21,325 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=4, 秒=21
2025-05-29 10:04:21,326 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:04:21,326 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:04:21.325038 AND Reservation.end_datetime > 2025-05-29 10:04:21.325038
2025-05-29 10:04:21,327 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:04:21,327 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:04:21,327 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:04:21.325038
2025-05-29 10:04:21,328 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:04:21,328 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:04:21,328 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:05:21,337 - root - INFO - 执行预约状态更新任务
2025-05-29 10:05:21,338 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:05:21.338086
2025-05-29 10:05:21,338 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=5, 秒=21
2025-05-29 10:05:21,339 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:05:21,339 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:05:21.338086 AND Reservation.end_datetime > 2025-05-29 10:05:21.338086
2025-05-29 10:05:21,340 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:05:21,340 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:05:21,341 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:05:21.338086
2025-05-29 10:05:21,341 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:05:21,342 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:05:21,342 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:06:21,346 - root - INFO - 执行预约状态更新任务
2025-05-29 10:06:21,347 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:06:21.347156
2025-05-29 10:06:21,347 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=6, 秒=21
2025-05-29 10:06:21,348 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:06:21,348 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:06:21.347156 AND Reservation.end_datetime > 2025-05-29 10:06:21.347156
2025-05-29 10:06:21,349 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:06:21,349 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:06:21,350 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:06:21.347156
2025-05-29 10:06:21,350 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:06:21,351 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:06:21,351 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:07:21,354 - root - INFO - 执行预约状态更新任务
2025-05-29 10:07:21,355 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:07:21.355176
2025-05-29 10:07:21,355 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=7, 秒=21
2025-05-29 10:07:21,356 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:07:21,356 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:07:21.355176 AND Reservation.end_datetime > 2025-05-29 10:07:21.355176
2025-05-29 10:07:21,357 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:07:21,357 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:07:21,358 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:07:21.355176
2025-05-29 10:07:21,358 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:07:21,359 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:07:21,359 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:08:21,371 - root - INFO - 执行预约状态更新任务
2025-05-29 10:08:21,372 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:08:21.372202
2025-05-29 10:08:21,372 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=8, 秒=21
2025-05-29 10:08:21,373 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:08:21,373 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:08:21.372202 AND Reservation.end_datetime > 2025-05-29 10:08:21.372202
2025-05-29 10:08:21,374 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:08:21,374 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:08:21,374 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:08:21.372202
2025-05-29 10:08:21,375 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:08:21,375 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:08:21,375 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:09:21,386 - root - INFO - 执行预约状态更新任务
2025-05-29 10:09:21,387 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:09:21.387503
2025-05-29 10:09:21,387 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=9, 秒=21
2025-05-29 10:09:21,388 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:09:21,388 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:09:21.387503 AND Reservation.end_datetime > 2025-05-29 10:09:21.387503
2025-05-29 10:09:21,389 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:09:21,389 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:09:21,390 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:09:21.387503
2025-05-29 10:09:21,390 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:09:21,391 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:09:21,391 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:10:21,396 - root - INFO - 执行预约状态更新任务
2025-05-29 10:10:21,396 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:10:21.396832
2025-05-29 10:10:21,397 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=10, 秒=21
2025-05-29 10:10:21,398 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:10:21,398 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:10:21.396832 AND Reservation.end_datetime > 2025-05-29 10:10:21.396832
2025-05-29 10:10:21,399 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:10:21,399 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:10:21,399 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:10:21.396832
2025-05-29 10:10:21,400 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:10:21,400 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:10:21,401 - backend.utils.status_updater - INFO - 没有预约需要更新状态
2025-05-29 10:11:21,404 - root - INFO - 执行预约状态更新任务
2025-05-29 10:11:21,404 - backend.utils.status_updater - INFO - 当前时间: 2025-05-29 10:11:21.404540
2025-05-29 10:11:21,404 - backend.utils.status_updater - INFO - 当前时间详细信息: 年=2025, 月=5, 日=29, 时=10, 分=11, 秒=21
2025-05-29 10:11:21,405 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'使用中'的预约
2025-05-29 10:11:21,405 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status == 'confirmed' AND Reservation.start_datetime <= 2025-05-29 10:11:21.404540 AND Reservation.end_datetime > 2025-05-29 10:11:21.404540
2025-05-29 10:11:21,406 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'使用中'的预约
2025-05-29 10:11:21,406 - backend.utils.status_updater - INFO - 执行查询：查找应该更新为'已过期'的预约
2025-05-29 10:11:21,406 - backend.utils.status_updater - INFO - SQL查询条件: Reservation.status.in_(['confirmed', 'in_use']) AND Reservation.end_datetime <= 2025-05-29 10:11:21.404540
2025-05-29 10:11:21,407 - backend.utils.status_updater - INFO - 找到 0 个应该更新为'已过期'的预约
2025-05-29 10:11:21,407 - backend.utils.status_updater - INFO - ORM更新结果: 使用中=0条, 已过期=0条
2025-05-29 10:11:21,408 - backend.utils.status_updater - INFO - 没有预约需要更新状态
